#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新新浪财经数据服务
基于最新的新浪财经API接口重新实现
"""

import asyncio
import aiohttp
import json
import logging
import time
import re
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class NewSinaStockInfo:
    """新浪股票信息"""
    symbol: str
    name: str
    current_price: float
    open_price: float
    close_price: float
    high_price: float
    low_price: float
    volume: int
    amount: float
    change: float
    change_percent: float
    bid_prices: List[float] = None
    ask_prices: List[float] = None
    raw_data: dict = None

@dataclass
class SinaCompanyFinance:
    """新浪公司财务数据"""
    symbol: str
    revenue: float
    profit: float
    pe_ratio: float
    pb_ratio: float
    roe: float

class NewSinaService:
    """新新浪财经数据服务"""
    
    def __init__(self):
        """初始化新新浪财经服务"""
        self.logger = logging.getLogger(__name__)
        
        # 基于你提供的新浪API端点
        self.api_endpoints = {
#             'realtime': 'https://hq.sinajs.cn/list=',  # 实时数据  # 已禁用新浪API - 超时问题
            'macd_stocks': 'https://statistic.cj.sina.com.cn/api/macd/stocks_by_rule',  # MACD选股
            'company_finance': 'https://finance.sina.com.cn/realstock/company/{}/iframe/cwbljzcsyl.html',  # 财务数据
            'company_revenue': 'https://finance.sina.com.cn/realstock/company/{}/iframe/cwblzyywsr.html',  # 营收数据
            'fund_data': 'https://finance.sina.com.cn/tgdata/fund_tg_api.json',  # 基金数据
            'market_data': 'https://finance.sina.com.cn/tgdata/toujiao/tg.json',  # 市场数据
            'live_pic': 'https://finance.sina.com.cn/live/js/pic_live.js',  # 实时图片
            'stock_data': 'https://finance.sina.com.cn/tgdata/stock_tg_api.json'  # 股票数据
        }
        
        # 请求配置
        self.request_interval = 1.5  # 1.5秒间隔
        self.timeout = 10
        self.max_retries = 3
        
        # 请求头 - 模拟新浪官方
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://finance.sina.com.cn/',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # 缓存系统
        self.cache = {}
        self.cache_ttl = 30  # 缓存30秒
        self.last_request_time = 0
        
        self.logger.info("新新浪财经服务初始化完成")
        self.logger.info(f"API端点: {len(self.api_endpoints)}个")
        self.logger.info(f"请求间隔: {self.request_interval}秒")

    async def _make_request(self, url: str, params: dict = None) -> Optional[dict]:
        """发起HTTP请求"""
        # 控制请求频率
        current_time = time.time()
        if current_time - self.last_request_time < self.request_interval:
            await asyncio.sleep(self.request_interval - (current_time - self.last_request_time))
        
        self.last_request_time = time.time()
        
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.get(url, headers=self.headers, params=params) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '')
                            text = await response.text()
                            
                            # 记录原始响应用于调试
                            self.logger.debug(f"响应内容: {text[:200]}...")
                            
                            if 'application/json' in content_type:
                                return await response.json()
                            else:
                                # 尝试解析为JSON
                                try:
                                    return json.loads(text)
                                except:
                                    return {'raw_text': text, 'status': 'success'}
                        else:
                            self.logger.warning(f"请求失败，状态码: {response.status}, URL: {url}")
                            
            except Exception as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)
        
        return None

    async def get_stock_realtime(self, symbol: str) -> Optional[NewSinaStockInfo]:
        """获取股票实时数据"""
        try:
            # 构建实时数据URL
            url = f"{self.api_endpoints['realtime']}{symbol}"
            
            self.logger.info(f"获取 {symbol} 实时数据: {url}")
            
            result = await self._make_request(url)
            
            if result and 'raw_text' in result:
                # 解析新浪财经返回的数据格式
                text = result['raw_text']
                return self._parse_realtime_data(text, symbol)
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取 {symbol} 实时数据失败: {e}")
            return None

    def _parse_realtime_data(self, text: str, symbol: str) -> Optional[NewSinaStockInfo]:
        """解析实时数据"""
        try:
            self.logger.debug(f"解析 {symbol} 数据，原始文本: {text[:100]}...")
            
            # 新浪财经返回格式: var hq_str_股票代码="数据";
            pattern = rf'var hq_str_{symbol}="([^"]*)"'
            match = re.search(pattern, text)
            
            if not match:
                self.logger.warning(f"未找到 {symbol} 的数据")
                return None
            
            data_str = match.group(1)
            fields = data_str.split(',')
            
            if len(fields) < 10:
                self.logger.warning(f"{symbol} 数据字段不足: {len(fields)}")
                return None
            
            # 解析新浪财经数据字段
            # [0]=股票名称, [1]=开盘价, [2]=昨收价, [3]=当前价, [4]=最高价, [5]=最低价, [6]=买一价, [7]=卖一价, [8]=成交量, [9]=成交额...
            stock_info = NewSinaStockInfo(
                symbol=symbol,
                name=fields[0] if len(fields) > 0 else symbol,
                open_price=self._safe_float(fields[1]) if len(fields) > 1 else 0,
                close_price=self._safe_float(fields[2]) if len(fields) > 2 else 0,
                current_price=self._safe_float(fields[3]) if len(fields) > 3 else 0,
                high_price=self._safe_float(fields[4]) if len(fields) > 4 else 0,
                low_price=self._safe_float(fields[5]) if len(fields) > 5 else 0,
                volume=self._safe_int(fields[8]) if len(fields) > 8 else 0,
                amount=self._safe_float(fields[9]) if len(fields) > 9 else 0,
                change=0,
                change_percent=0,
                raw_data={'fields': fields, 'raw': data_str}
            )
            
            # 提取买卖盘数据
            if len(fields) > 20:
                try:
                    stock_info.bid_prices = [self._safe_float(fields[i]) for i in [11, 13, 15, 17, 19]]
                    stock_info.ask_prices = [self._safe_float(fields[i]) for i in [21, 23, 25, 27, 29]]
                except:
                    return await self._implement_real_logic()